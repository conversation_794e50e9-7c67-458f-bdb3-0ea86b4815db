'use client';

import {
	<PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	<PERSON><PERSON><PERSON>nt,
	<PERSON><PERSON><PERSON>er,
	CardTitle,
	Translate,
} from '@/components/ui';
import { cn, getTranslationKeyOfLanguage } from '@/lib';
import { RandomWord, WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { Plus, Undo2 } from 'lucide-react';
import { memo, useCallback } from 'react';
import { WordNetInfo, WordNetSummary } from '@/components/wordnet';

interface WordItemProps {
	word: RandomWord;
	details: WordDetail | undefined;
	loadingState: { gettingDetail?: boolean; adding?: boolean; generatingExamples?: boolean };
	onGetDetails: (word: RandomWord) => Promise<void>;
	onAddToCollection: (word: RandomWord) => Promise<void>;
	onUndoWordAddition?: (word: RandomWord) => Promise<void>;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	isAdded: boolean;
	sourceLanguage: Language;
	targetLanguage: Language;
	onSearchWordNetTerm?: (term: string) => void;
}

function WordItem({
	word,
	details,
	loadingState,
	onGetDetails,
	onAddToCollection,
	onUndoWordAddition,
	onGenerateExamples,
	isAdded,
	sourceLanguage,
	targetLanguage,
	onSearchWordNetTerm,
}: WordItemProps) {
	console.log(
		'🎯 WordItem received onSearchWordNetTerm for',
		word.term,
		':',
		!!onSearchWordNetTerm
	);
	return (
		<Card
			key={word.term}
			className="flex flex-col break-inside-avoid shadow-lg border border-border bg-background hover:shadow-xl transition-shadow duration-200"
		>
			<CardHeader className="py-4 px-5 border-b border-border bg-gradient-to-r from-primary/5 via-primary/10 to-transparent rounded-t-lg">
				<CardTitle className="flex justify-between items-start gap-3">
					<div className="flex-1">
						<h3 className="text-3xl font-bold tracking-tight text-primary drop-shadow-sm mb-2">
							{word.term}
						</h3>
						<div className="flex flex-wrap items-center gap-2 mb-2">
							{word.partOfSpeech && word.partOfSpeech.length > 0 && (
								<div className="flex flex-wrap gap-1">
									{word.partOfSpeech.map((pos, index) => (
										<Badge
											key={index}
											variant="secondary"
											className="text-xs font-medium"
										>
											{pos}
										</Badge>
									))}
								</div>
							)}
							{/* WordNet Summary for detailed words */}
							{details?.WordNetData && (
								<WordNetSummary wordNetData={details.WordNetData} />
							)}
						</div>
					</div>
				</CardTitle>
			</CardHeader>
			<CardContent className="flex-grow flex flex-col p-5">
				<div className="space-y-4 flex-grow">
					{/* Basic meaning from RandomWord */}
					{!details && word.meaning && word.meaning.length > 0 && (
						<div className="space-y-2">
							{word.meaning.map((meaning: Record<Language, string>, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									<div className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1">
										<p className="text-xs font-medium text-muted-foreground tracking-wide">
											<Translate
												text={getTranslationKeyOfLanguage(targetLanguage)}
											/>
											:
										</p>
										<p className="mb-1 text-sm text-foreground/95">
											{meaning[targetLanguage] || (
												<span className="italic opacity-70">
													<Translate text="words.meaning_not_provided" />
												</span>
											)}
										</p>
										<p className="text-xs font-medium text-muted-foreground tracking-wide">
											<Translate
												text={getTranslationKeyOfLanguage(sourceLanguage)}
											/>
											:
										</p>
										<p className="text-sm text-foreground/95">
											{meaning[sourceLanguage] || (
												<span className="italic opacity-70">
													<Translate text="words.translation_not_provided" />
												</span>
											)}
										</p>
									</div>
								</div>
							))}
						</div>
					)}

					{/* Detailed information from WordDetail */}
					{details && details.definitions && details.definitions.length > 0
						? details.definitions.map((definition, index) => (
								<div
									key={index}
									className="p-4 rounded-xl border border-border/70 bg-accent/25 hover:bg-accent/40 transition-colors duration-150 dark:bg-accent/15 dark:hover:bg-accent/30"
								>
									{definition.pos && definition.pos.length > 0 && (
										<p className="text-xs font-semibold uppercase tracking-wider text-primary/90 mb-2.5">
											{definition.pos.join(', ')}
										</p>
									)}
									{definition.ipa && (
										<p className="text-sm text-muted-foreground italic mb-2.5">
											IPA: {definition.ipa}
										</p>
									)}
									{/* Explanations section */}
									{definition.explains && definition.explains.length > 0 ? (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												<Translate text="words.explanations" />:
											</p>
											{definition.explains.map((explain, expIndex) => (
												<div
													key={expIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-primary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																targetLanguage
															)}
														/>
														:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{explain[targetLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.explanation_not_provided" />
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																sourceLanguage
															)}
														/>
														:
													</p>
													<p className="text-sm text-foreground/95">
														{explain[sourceLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.translation_not_provided" />
															</span>
														)}
													</p>
												</div>
											))}
										</div>
									) : (
										<div className="mb-3">
											<p className="text-sm font-semibold text-muted-foreground mb-1.5">
												<Translate text="words.explanations" />:
											</p>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												<Translate text="words.no_explanations_provided" />
											</p>
										</div>
									)}

									{/* Examples section */}
									{definition.examples && definition.examples.length > 0 ? (
										<div>
											<div className="flex items-center justify-between mb-1.5">
												<p className="text-sm font-semibold text-muted-foreground">
													<Translate text="words.examples" />:
												</p>
												{onGenerateExamples && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() => onGenerateExamples(word)}
														loading={loadingState.generatingExamples}
														className="h-6 px-2 text-xs"
													>
														{!loadingState.generatingExamples && (
															<Plus className="h-3 w-3" />
														)}
														<span className="ml-1">
															<Translate
																text={
																	loadingState.generatingExamples
																		? 'words.generating_examples'
																		: 'words.add_more_examples'
																}
															/>
														</span>
													</Button>
												)}
											</div>
											{definition.examples.map((example, exIndex) => (
												<div
													key={exIndex}
													className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
												>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																targetLanguage
															)}
														/>
														:
													</p>
													<p className="mb-1 text-sm text-foreground/95">
														{example[targetLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.example_not_provided" />
															</span>
														)}
													</p>
													<p className="text-xs font-medium text-muted-foreground tracking-wide">
														<Translate
															text={getTranslationKeyOfLanguage(
																sourceLanguage
															)}
														/>
														:
													</p>
													<p className="text-sm text-foreground/95">
														{example[sourceLanguage] || (
															<span className="italic opacity-70">
																<Translate text="words.translation_not_provided" />
															</span>
														)}
													</p>
												</div>
											))}
										</div>
									) : (
										<div>
											<div className="flex items-center justify-between mb-1.5">
												<p className="text-sm font-semibold text-muted-foreground">
													<Translate text="words.examples" />:
												</p>
												{onGenerateExamples && (
													<Button
														variant="ghost"
														size="sm"
														onClick={() => onGenerateExamples(word)}
														loading={loadingState.generatingExamples}
														className="h-6 px-2 text-xs"
													>
														{!loadingState.generatingExamples && (
															<Plus className="h-3 w-3" />
														)}
														<span className="ml-1">
															<Translate
																text={
																	loadingState.generatingExamples
																		? 'words.generating_examples'
																		: 'words.generate_examples'
																}
															/>
														</span>
													</Button>
												)}
											</div>
											<p className="p-2 text-sm text-muted-foreground italic opacity-70">
												<Translate text="words.no_examples_provided" />
											</p>
										</div>
									)}
								</div>
						  ))
						: details && (
								<p className="p-4 text-sm text-muted-foreground italic">
									<Translate text="words.no_definitions_available" />
								</p>
						  )}
				</div>

				{/* WordNet Information */}
				{details?.WordNetData && (
					<div className="mt-4">
						<WordNetInfo
							wordNetData={details.WordNetData}
							term={word.term}
							className="border-0 shadow-none bg-transparent p-0"
							onSearchTerm={(term) => {
								console.log('🎯 WordItem passing term to callback:', term);
								console.log(
									'🎯 WordItem callback function:',
									!!onSearchWordNetTerm
								);
								onSearchWordNetTerm?.(term);
							}}
						/>
					</div>
				)}

				{/* Action buttons */}
				<div className="mt-auto flex flex-col space-y-2 pt-5">
					{!details && !loadingState.gettingDetail && (
						<Button
							variant="outline"
							size="sm"
							onClick={() => onGetDetails(word)}
							className="w-full flex items-center justify-center gap-2"
						>
							<Translate text="words.get_details" />
						</Button>
					)}
					{loadingState.gettingDetail && (
						<Button
							variant="outline"
							size="sm"
							loading={true}
							className="w-full flex items-center justify-center gap-2"
						>
							<Translate text="words.getting_details" />
						</Button>
					)}
					{isAdded ? (
						<Button
							variant="outline"
							size="sm"
							onClick={() => onUndoWordAddition?.(word)}
							className="w-full flex items-center justify-center gap-2 border-orange-200 text-orange-700 bg-orange-50 hover:bg-orange-100"
						>
							<Undo2 size={16} />
							<span>
								<Translate text="words.undo" />
							</span>
						</Button>
					) : (
						<Button
							variant="default"
							size="sm"
							onClick={() => onAddToCollection(word)}
							loading={loadingState.adding}
							className="w-full flex items-center justify-center gap-2"
						>
							{!loadingState.adding && <Plus size={16} />}
							<span>
								{loadingState.adding ? (
									<Translate text="words.adding" />
								) : (
									<Translate text="words.add_to_collection" />
								)}
							</span>
						</Button>
					)}
				</div>
			</CardContent>
		</Card>
	);
}

interface WordListProps {
	words: RandomWord[] | undefined;
	detailedWords: Record<string, WordDetail>;
	onGetDetails: (word: RandomWord) => Promise<void>;
	getLoadingState: (term: string) => {
		gettingDetail?: boolean;
		adding?: boolean;
		generatingExamples?: boolean;
	};
	onAddToCollection: (word: RandomWord) => Promise<void>;
	onUndoWordAddition?: (word: RandomWord) => Promise<void>;
	onGenerateExamples?: (word: RandomWord) => Promise<void>;
	addedWords: Set<string>;
	className?: string;
	sourceLanguage: Language;
	targetLanguage: Language;
	// Load more functionality
	isLoadingMore?: boolean;
	onLoadMore?: () => Promise<void>;
	// WordNet search functionality
	onSearchWordNetTerm?: (term: string) => void;
}

function WordListComponent({
	words,
	detailedWords,
	onGetDetails,
	getLoadingState,
	onAddToCollection,
	onUndoWordAddition,
	onGenerateExamples,
	addedWords,
	className,
	sourceLanguage, // Default fallback
	targetLanguage, // Default fallback
	isLoadingMore = false,
	onLoadMore,
	onSearchWordNetTerm,
}: WordListProps) {
	console.log('📝 WordList received onSearchWordNetTerm:', !!onSearchWordNetTerm);
	console.log('📝 WordList onSearchWordNetTerm type:', typeof onSearchWordNetTerm);
	console.log('📝 WordList onSearchWordNetTerm value:', onSearchWordNetTerm);

	// Capture callback to avoid closure issues
	const searchCallback = onSearchWordNetTerm;
	const handleGetDetails = useCallback(
		async (word: RandomWord) => {
			if (detailedWords[word.term] || getLoadingState(word.term).gettingDetail) return;
			await onGetDetails(word);
		},
		[detailedWords, getLoadingState, onGetDetails]
	);

	const handleAddToCollection = useCallback(
		async (word: RandomWord) => {
			if (getLoadingState(word.term).adding) return;
			await onAddToCollection(word);
		},
		[getLoadingState, onAddToCollection]
	);

	if (!words || words.length === 0) return null;

	return (
		<div className={cn('space-y-4', className)}>
			{words?.map((word) => {
				const loadingState = getLoadingState(word.term);
				const details = detailedWords[word.term];

				console.log(`📝 WordList mapping ${word.term} - callback:`, !!onSearchWordNetTerm);

				return (
					<WordItem
						key={word.term}
						word={word}
						details={details}
						loadingState={loadingState}
						onGetDetails={handleGetDetails}
						onAddToCollection={handleAddToCollection}
						onUndoWordAddition={onUndoWordAddition}
						onGenerateExamples={onGenerateExamples}
						isAdded={addedWords.has(word.term)}
						sourceLanguage={sourceLanguage}
						targetLanguage={targetLanguage}
						onSearchWordNetTerm={onSearchWordNetTerm}
					/>
				);
			})}

			{/* Load More Button - Always show if onLoadMore is provided */}
			{onLoadMore && (
				<div className="flex justify-center pt-6">
					<Button
						variant="outline"
						size="lg"
						onClick={onLoadMore}
						loading={isLoadingMore}
						className="min-w-[200px] h-12 text-base font-medium rounded-xl border-2 border-primary/20 hover:border-primary/40 hover:bg-primary/5 transition-all duration-200"
					>
						{!isLoadingMore && <Plus size={18} className="mr-2" />}
						<Translate
							text={isLoadingMore ? 'words.loading_more' : 'words.load_more'}
						/>
					</Button>
				</div>
			)}
		</div>
	);
}

const arePropsEqual = (prevProps: WordListProps, nextProps: WordListProps) => {
	return (
		(prevProps.words?.length ?? 0) === (nextProps.words?.length ?? 0) &&
		(prevProps.words?.every((word, index) => word.term === nextProps.words?.[index]?.term) ??
			true) &&
		Object.keys(prevProps.detailedWords).length ===
			Object.keys(nextProps.detailedWords).length &&
		prevProps.className === nextProps.className &&
		prevProps.sourceLanguage === nextProps.sourceLanguage &&
		prevProps.targetLanguage === nextProps.targetLanguage &&
		prevProps.isLoadingMore === nextProps.isLoadingMore &&
		// Shallow compare functions assuming they are stable (e.g., from useCallback with stable deps)
		prevProps.onGetDetails === nextProps.onGetDetails &&
		prevProps.getLoadingState === nextProps.getLoadingState &&
		prevProps.onAddToCollection === nextProps.onAddToCollection &&
		prevProps.onUndoWordAddition === nextProps.onUndoWordAddition &&
		prevProps.onLoadMore === nextProps.onLoadMore &&
		prevProps.onSearchWordNetTerm === nextProps.onSearchWordNetTerm
	);
};

// Temporarily disable memo for debugging
export const WordList = WordListComponent;
// export const WordList = memo(WordListComponent, arePropsEqual);
